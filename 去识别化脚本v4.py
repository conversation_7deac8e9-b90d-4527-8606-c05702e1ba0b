import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import pandas as pd
import re
import os
import sys
from docx import Document
import threading
from datetime import datetime
import shutil
import openpyxl
from openpyxl import load_workbook

"""
去识别化工具
功能：对员工信息以及日期进行加密和解密处理
作者：Lingxi Yuan
日期：2025年6月28日
版本：v4.0
"""

# ================== 资源路径处理 ==================
def get_database_path():
    """获取数据库文件路径，优先使用程序同目录下的文件"""
    # 1. 获取程序所在目录
    if getattr(sys, 'frozen', False):
        # exe环境
        program_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        program_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 2. 检查程序目录下的数据库文件
    db_path = os.path.join(program_dir, '技术资格管理.xls')
    if os.path.exists(db_path):
        return db_path
    
    # 3. 检查当前工作目录
    db_path = os.path.join(os.getcwd(), '技术资格管理.xls')
    if os.path.exists(db_path):
        return db_path
    
    # 4. 都没找到，返回默认路径（用于后续处理）
    return os.path.join(program_dir, '技术资格管理.xls')

def get_program_directory():
    """获取程序所在目录"""
    if getattr(sys, 'frozen', False):
        # exe环境
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))

def get_work_directories():
    """获取工作目录路径"""
    program_dir = get_program_directory()
    input_dir = os.path.join(program_dir, "待处理文件夹")
    output_dir = os.path.join(program_dir, "处理后文件夹")
    log_dir = os.path.join(program_dir, "log文件夹")  # 新增日志文件夹
    return input_dir, output_dir, log_dir

def ensure_work_directories():
    """确保工作目录存在"""
    input_dir, output_dir, log_dir = get_work_directories()
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)  # 确保日志文件夹存在
    return input_dir, output_dir, log_dir

def get_database_version(db_path):
    """获取数据库文件的版本信息（最后修改时间）"""
    try:
        if os.path.exists(db_path):
            mtime = os.path.getmtime(db_path)
            return datetime.fromtimestamp(mtime).strftime("%Y年%m月%d日 %H:%M:%S")
        else:
            return "文件不存在"
    except Exception as e:
        return f"获取失败: {str(e)}"

def get_database_size(db_path):
    """获取数据库文件大小"""
    try:
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size/1024:.1f} KB"
            else:
                return f"{size/(1024*1024):.1f} MB"
        else:
            return "文件不存在"
    except Exception as e:
        return f"获取失败: {str(e)}"

# ================== 检查.doc文件的函数 ==================
def check_for_doc_files(directory):
    """检查目录中是否有.doc文件"""
    doc_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.doc') and not file.lower().endswith('.docx'):
                rel_path = os.path.relpath(os.path.join(root, file), directory)
                doc_files.append(rel_path)
    return doc_files

# ================== 验证人名代码的函数 ==================
def validate_employee_code(code):
    """验证人名代码是否为4位"""
    if not code:
        return True  # 空代码不验证
    
    # 去掉N_前缀，检查后面的4位
    code_part = code[2:]  # 去掉 "N_"

    # 检查是否为4位字符
    if len(code_part) != 4:
        return False
    
    if not code.startswith("N_"):  # 新增前缀检查
        return False
    code = code[2:]  # 去掉N_前缀
   
    # 检查是否为有效字符集
    CHARSET = '0123456789ABCDEFGHJKLMNPQRSTUVWXYZ'  # 34进制字符集（无I,O）
    for char in code:
        if char not in CHARSET:
            return False
    
    return True

def find_invalid_employee_codes_in_text(text):
    """在文本中查找无效的人名代码"""
    # 查找N_前缀的编码
    pattern = r'N_[0-9A-HJ-NP-Z]+'
    matches = re.finditer(pattern, text)
    
    invalid_codes = []
    for match in matches:
        code = match.group()
        try:
            # 验证编码格式
            if not code.startswith("N_"):
                invalid_codes.append(code)
                continue
                
            code_part = code[2:]  # 去掉 "N_" 前缀
            if len(code_part) != 4:
                invalid_codes.append(f"{code} (编码部分{len(code_part)}位，应为4位)")
                continue
                
            # 尝试解码验证
            decode_employee_id(code)
        except Exception as e:
            # 无法解码的代码是无效的
            invalid_codes.append(f"{code} (解码失败: {str(e)})")
    
    return invalid_codes

# ================== 你的加密解密算法 ==================
CHARSET = '0123456789ABCDEFGHJKLMNPQRSTUVWXYZ'  # 34进制字符集（无I,O）

def encode_employee_id(employee_id, offset=15000):
    value = (employee_id + offset) * 9
    digits = []
    num = value
    while num:
        digits.append(num % 34)
        num //= 34
    digits = digits[::-1]
    while len(digits) < 4:
        digits = [0] + digits
    encoded = ''.join(CHARSET[d] for d in digits)
    reversed_encoded = encoded[::-1]
    return "N_" + reversed_encoded  # 前缀N_表示人名编码

def decode_employee_id(encoded_id, offset=15000):
    if not encoded_id.startswith("N_"):
        raise ValueError("无效的编码前缀")
    # 先去掉前缀
    code_without_prefix = encoded_id[2:]
    
    if len(code_without_prefix) != 4:
        raise ValueError("编码长度必须为4")
    
    # 因为加密时进行了反转，所以解密时需要反转回来
    reversed_code = code_without_prefix[::-1]
    digits = [CHARSET.index(c) for c in reversed_code]
    value = 0
    for digit in digits:
        value = value * 34 + digit
    if value % 9 != 0:
        raise ValueError(f"无效的编码: {encoded_id[::-1]} (不能被9整除)")
    original_id = (value // 9) - offset
    if not (1 <= original_id <= 199999):
        raise ValueError(f"无效的工号: {original_id} (超出范围)")
    return original_id

DAY_CHARSET = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 
               'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 
               'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']

def encode_day(day):
    multiplied = day * 18
    if multiplied == 0:
        return "D_" + DAY_CHARSET[0] # 添加前缀D_表示日期编码
    digits = []
    num = multiplied
    while num > 0:
        remainder = num % 24
        digits.append(remainder)
        num = num // 24
    encoded = ''.join([DAY_CHARSET[d] for d in reversed(digits)])
    return "D_" + encoded # 添加前缀D_表示日期编码

def decode_day(encoded):
    if not encoded.startswith("D_"):
        raise ValueError("日期编码必须以'D_'开头")
    encoded = encoded[2:] # 去掉D_前缀
    char_to_val = {c: i for i, c in enumerate(DAY_CHARSET)}
    try:
        digits = [char_to_val[c] for c in encoded]
    except KeyError:
        raise ValueError("编码包含无效字符")
    num = 0
    for digit in digits:
        num = num * 24 + digit
    day = num // 18
    if num % 18 != 0:
        raise ValueError("无效的编码，不能整除18")
    return day

# ========== 文件名处理函数 ==========
def encrypt_filename(filename, db):
    name_pattern = r'^([^\d\-\.]+)'  # 汉字姓名在开头
    date_patterns = [
        r'(\d{4}年\d{1,2}月\d{1,2}日)',  # 2024年6月18日
        r'(\d{4}-\d{1,2}-\d{1,2})',      # 2024-06-18
        r'(\d{4}/\d{1,2}/\d{1,2})',      # 2024/06/18
        r'(\d{8})',                      # 20240618
    ]
    basename, ext = os.path.splitext(filename)

    # 1. 查找姓名
    name_match = re.match(name_pattern, basename)
    if name_match:
        name = name_match.group(1)
        try:
            emp_id = name_to_empid(db, name)
            name_enc = encode_employee_id(emp_id)  # 这里已经包含N_前缀
            basename = basename.replace(name, name_enc, 1)
        except:
            pass

    # 2. 查找并加密所有日期（修复：处理所有日期，不只是第一个）
    replacements = []
    used_positions = set()  # 避免重复匹配

    for pat in date_patterns:
        matches = re.finditer(pat, basename)
        for match in matches:
            start, end = match.start(), match.end()
            # 检查是否与已有匹配重叠
            if not any(pos in range(start, end) for pos in used_positions):
                date_str = match.group(1)
                try:
                    date_enc = encode_date_filename(date_str)  # 使用新的文件名专用加密函数
                    replacements.append((start, end, date_str, date_enc))
                    used_positions.update(range(start, end))
                except:
                    pass

    # 按位置倒序排列，从后往前替换
    replacements.sort(key=lambda x: x[0], reverse=True)

    # 执行替换
    for start, end, original, replacement in replacements:
        basename = basename[:start] + replacement + basename[end:]

    return basename + ext
def decrypt_filename(filename, db):
    basename, ext = os.path.splitext(filename)

    # 1. 解密姓名编码
    name_code_pat = r'N_[0-9A-HJ-NP-Z]{4}'  # 使用N_前缀
    m = re.search(name_code_pat, basename)
    if m:
        code = m.group()
        try:
            emp_id = decode_employee_id(code)
            info = empid_to_info(db, emp_id)
            basename = basename.replace(code, info['姓名'], 1)
        except:
            pass

    # 2. 解密所有日期编码（修复：处理所有日期，不只是第一个）
    date_code_patterns = [
        r'\d{4}年\d{1,2}月D_[A-Z]+日',  # 2024年6月D_AB日
        r'\d{1,2}月D_[A-Z]+日'          # 6月D_AB日
    ]

    replacements = []
    used_positions = set()  # 避免重复匹配

    for pattern in date_code_patterns:
        matches = re.finditer(pattern, basename)
        for match in matches:
            start, end = match.start(), match.end()
            # 检查是否与已有匹配重叠
            if not any(pos in range(start, end) for pos in used_positions):
                code = match.group()
                try:
                    date_dec = decode_date_filename(code)  # 使用新的文件名专用解密函数
                    replacements.append((start, end, code, date_dec))
                    used_positions.update(range(start, end))
                except Exception as e:
                    print(f"解密日期编码失败: {code}, 错误: {e}")

    # 按位置倒序排列，从后往前替换
    replacements.sort(key=lambda x: x[0], reverse=True)

    # 执行替换
    for start, end, original, replacement in replacements:
        basename = basename[:start] + replacement + basename[end:]

    return basename + ext

# ========== 日期处理函数 ==========
def extract_date_parts(date_str):
    """提取日期的年月日部分"""
    patterns = [
        r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})$',   # 2024-06-18
        r'(\d{4})(\d{2})(\d{2})$',               # 20240618
        r'(\d{4})年(\d{1,2})月(\d{1,2})日',      # 2024年6月18日
        r'(\d{1,2})月(\d{1,2})日',               # 2月11日
    ]
    date_str = date_str.replace(' ', '')
    for pat in patterns:
        m = re.search(pat, date_str)
        if m:
            groups = m.groups()
            if len(groups) == 3:
                year, month, day = groups
                return int(year), int(month), int(day)
            elif len(groups) == 2:
                # 月日格式，使用当前年份
                month, day = groups
                current_year = datetime.now().year
                return current_year, int(month), int(day)
    raise ValueError("请输入完整日期格式，如2024-06-18、20240618或2月11日")

def encode_date_full(date_str):
    """加密完整日期，返回格式：xxxx年x月编码日 或 x月编码日"""
    year, month, day = extract_date_parts(date_str)
    if not (1 <= day <= 31):
        raise ValueError(f"日期超出范围: {day}")
    encoded_day = encode_day(day)

    # 检查原始字符串是否包含年份
    if re.search(r'\d{4}', date_str):
        # 包含年份，返回完整格式
        return f"{year}年{month}月{encoded_day}日"
    else:
        # 不包含年份，返回月日格式
        return f"{month}月{encoded_day}日"

def encode_date_filename(date_str):
    """专门用于文件名的日期加密，统一转换为中文格式便于阅读"""
    year, month, day = extract_date_parts(date_str)
    if not (1 <= day <= 31):
        raise ValueError(f"日期超出范围: {day}")
    encoded_day = encode_day(day)

    # 统一返回中文格式，便于阅读
    return f"{year}年{month}月{encoded_day}日"

def decode_date_full(encoded_date_str):
    """解密完整日期格式"""
    encoded_date_str = encoded_date_str.strip()

    # 支持多种格式
    patterns = [
        (r'(\d{4})年(\d{1,2})月(D_[A-Z]+)日', 'full'),    # 2024年6月D_XX日
        (r'(\d{1,2})月(D_[A-Z]+)日', 'short')              # 6月D_XX日
    ]

    for pattern, format_type in patterns:
        match = re.match(pattern, encoded_date_str)
        if match:
            if format_type == 'full':
                year, month, encoded_day = match.groups()
                day = decode_day(encoded_day)
                return f"{year}年{month}月{day}日"
            else:  # short format
                month, encoded_day = match.groups()
                day = decode_day(encoded_day)
                return f"{month}月{day}日"

    raise ValueError(f"无法识别的日期格式: {encoded_date_str}")

def decode_date_filename(encoded_date_str):
    """专门用于文件名的日期解密，统一使用中文格式"""
    encoded_date_str = encoded_date_str.strip()

    # 支持中文格式
    patterns = [
        (r'(\d{4})年(\d{1,2})月(D_[A-Z]+)日', 'full'),    # 2024年6月D_XX日
        (r'(\d{1,2})月(D_[A-Z]+)日', 'short')              # 6月D_XX日
    ]

    for pattern, format_type in patterns:
        match = re.match(pattern, encoded_date_str)
        if match:
            if format_type == 'full':
                year, month, encoded_day = match.groups()
                day = decode_day(encoded_day)
                return f"{year}年{month}月{day}日"
            else:  # short format
                month, encoded_day = match.groups()
                day = decode_day(encoded_day)
                return f"{month}月{day}日"

    raise ValueError(f"无法识别的日期格式: {encoded_date_str}")

# =========== 员工信息表（数据库）读取 ===========
def load_employee_db(path=None):
    if path is None:
        path = get_database_path()
    df = pd.read_excel(path)
    df = df[df['有效性'] == '有效'].copy()
    # 确保员工号为整数类型
    df['员工号'] = df['员工号'].astype(int)
    return df

# 查询姓名-工号
def name_to_empid(df, name):
    result = df[df['姓名'] == name]
    if result.empty:
        raise ValueError(f"未找到姓名: {name}")
    emp_id = int(result.iloc[0]['员工号'])
    return emp_id

# 查询工号-详细信息 (修复版本)
def empid_to_info(df, emp_id):
    # 确保emp_id为整数
    emp_id = int(emp_id)
    result = df[df['员工号'] == emp_id]
    
    if result.empty:
        raise ValueError(f"未找到工号: {emp_id}")
    
    row = result.iloc[0]
    return {
        '姓名': str(row['姓名']),
        '员工号': str(row['员工号']),
        '型别': str(row['型别']),
        '技术资格': str(row['技术资格']),
        '有效性': str(row['有效性'])
    }

# ========== 数据库版本确认对话框 ==========
class DatabaseConfirmDialog(tk.Toplevel):
    def __init__(self, parent, db_path, operation_name):
        super().__init__(parent)
        self.result = False
        self.db_path = db_path
        self.operation_name = operation_name
        
        self.title(f"确认数据库版本 - {operation_name}")
        self.geometry("700x750")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()
        
        # 居中显示
        self.center_window(parent)
        
        self.init_ui()
    
    def center_window(self, parent):
        """将窗口居中显示"""
        self.update_idletasks()
        
        # 获取屏幕尺寸
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # 获取窗口尺寸
        window_width = 700
        window_height = 750

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def init_ui(self):
        # 创建主容器，使用pack布局
        main_container = tk.Frame(self)
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题区域
        title_frame = tk.Frame(main_container)
        title_frame.pack(fill='x', pady=(0, 20))
        
        title_label = tk.Label(title_frame, text=f"即将开始{self.operation_name}", 
                              font=("微软雅黑", 16, "bold"), fg="blue")
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="请确认当前数据库版本信息", 
                                 font=("微软雅黑", 12), fg="gray")
        subtitle_label.pack(pady=(10, 0))
        
        # 数据库信息区域
        info_frame = tk.LabelFrame(main_container, text="当前数据库信息",                                   font=("微软雅黑", 11, "bold"))
        info_frame.pack(fill='x', pady=(0, 20))
        
        # 使用grid布局来对齐信息
        info_content = tk.Frame(info_frame)
        info_content.pack(fill='x', padx=15, pady=15)
        
        # 文件路径
        tk.Label(info_content, text="文件路径:", font=("微软雅黑", 10, "bold")).grid(
            row=0, column=0, sticky='w', pady=8)
        
        path_frame = tk.Frame(info_content)
        path_frame.grid(row=0, column=1, sticky='ew', padx=(10, 0), pady=8)
        info_content.grid_columnconfigure(1, weight=1)
        
        path_entry = tk.Entry(path_frame, font=("微软雅黑", 9))
        path_entry.pack(fill='x')
        path_entry.insert(0, self.db_path)
        path_entry.config(state='readonly', bg='#f8f8f8')
        
        # 获取文件信息
        try:
            file_stat = os.stat(self.db_path)
            mod_time = datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y年%m月%d日 %H:%M:%S")
            file_size = f"{file_stat.st_size / 1024 / 1024:.1f} MB"
            
            # 加载数据库获取记录数
            db = load_employee_db(self.db_path)
            record_count = len(db)
            status_text = f"正常 (共{record_count}条有效记录)"
            status_color = "green"
            
        except Exception as e:
            mod_time = "无法获取"
            file_size = "无法获取"
            status_text = f"错误: {str(e)}"
            status_color = "red"
        
        # 最后修改时间
        tk.Label(info_content, text="最后修改:", font=("微软雅黑", 10, "bold")).grid(
            row=1, column=0, sticky='w', pady=8)
        tk.Label(info_content, text=mod_time, font=("微软雅黑", 10), fg="green").grid(
            row=1, column=1, sticky='w', padx=(10, 0), pady=8)
        
        # 文件大小
        tk.Label(info_content, text="文件大小:", font=("微软雅黑", 10, "bold")).grid(
            row=2, column=0, sticky='w', pady=8)
        tk.Label(info_content, text=file_size, font=("微软雅黑", 10), fg="green").grid(
            row=2, column=1, sticky='w', padx=(10, 0), pady=8)
        
        # 文件状态
        tk.Label(info_content, text="文件状态:", font=("微软雅黑", 10, "bold")).grid(
            row=3, column=0, sticky='w', pady=8)
        tk.Label(info_content, text=status_text, font=("微软雅黑", 10), fg=status_color).grid(
            row=3, column=1, sticky='w', padx=(10, 0), pady=8)
        
        # 重要提示区域
        warning_frame = tk.LabelFrame(main_container, text="重要提示", 
                                     font=("微软雅黑", 11, "bold"))
        warning_frame.pack(fill='x', pady=(0, 20))
        
        warning_content = tk.Frame(warning_frame)
        warning_content.pack(fill='x', padx=15, pady=15)
        
        warnings = [
            "• 如果版本过期，建议先更新数据库再进行操作",
            "• 使用过时的数据库可能会导致加密/解密结果不准确",
            "• 建议定期检查和更新数据库文件",
            "• 更新之后请把数据库文件放在程序同目录（文件夹）下",
        ]
        
        for warning in warnings:
            tk.Label(warning_content, text=warning, font=("微软雅黑", 10), 
                    fg="#FF6600", justify='left').pack(anchor='w', pady=2)
        
        # 按钮区域 - 固定在底部
        button_frame = tk.Frame(main_container)
        button_frame.pack(fill='x', pady=(20, 0))
        
        # 统一的按钮样式
        button_style = {
            'bg': '#E0E0E0',
            'fg': '#424242', 
            'relief': 'raised',
            'bd': 1,
            'activebackground': '#D0D0D0',
            'activeforeground': '#212121'
        }
        
        update_button_style = {
            'bg': '#F5F5F5',
            'fg': '#424242',
            'relief': 'raised', 
            'bd': 1,
            'activebackground': '#E0E0E0',
            'activeforeground': '#212121'
        }
        
        confirm_button_style = {
            'bg': '#C8E6C9',
            'fg': '#2E7D32',
            'relief': 'raised',
            'bd': 1,
            'activebackground': '#A5D6A7',
            'activeforeground': '#1B5E20'
        }
        
        # 左侧按钮
        left_buttons = tk.Frame(button_frame)
        left_buttons.pack(side='left')
        
        update_btn = tk.Button(left_buttons, text="更新数据库", width=12, 
                              font=("微软雅黑", 10), 
                              command=self.update_database, **update_button_style)
        update_btn.pack(side='left', padx=(0, 10))
        
        # 右侧按钮
        right_buttons = tk.Frame(button_frame)
        right_buttons.pack(side='right')
        
        cancel_btn = tk.Button(right_buttons, text="取消", width=12, 
                              font=("微软雅黑", 10), 
                              command=self.cancel, **button_style)
        cancel_btn.pack(side='left', padx=(0, 10))
        
        confirm_btn = tk.Button(right_buttons, text=f"确认{self.operation_name}", width=15, 
                               font=("微软雅黑", 10), 
                               command=self.confirm, **confirm_button_style)
        confirm_btn.pack(side='left')

    def update_database(self):
        """更新数据库文件"""
        file_path = filedialog.askopenfilename(
            title="选择新的数据库文件",
            filetypes=[("Excel文件", "*.xls *.xlsx"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            # 验证文件格式
            test_df = pd.read_excel(file_path)
            required_columns = ['姓名', '员工号', '有效性']
            missing_columns = [col for col in required_columns if col not in test_df.columns]
            
            if missing_columns:
                messagebox.showerror("文件格式错误", 
                                   f"所选文件缺少必要的列: {', '.join(missing_columns)}")
                return
            
            # 备份原文件
            backup_path = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(self.db_path, backup_path)
            
            # 复制新文件
            shutil.copy2(file_path, self.db_path)
            
            messagebox.showinfo("更新成功", 
                              f"数据库已更新\n原文件已备份为:\n{backup_path}")
            
            # 关闭对话框并返回成功
            self.result = True
            self.destroy()
            
        except Exception as e:
            messagebox.showerror("更新失败", f"无法更新数据库: {str(e)}")

    def confirm(self):
        """确认使用当前数据库"""
        self.result = True
        self.destroy()

    def cancel(self):
        """取消操作"""
        self.result = False
        self.destroy()

# ========== 文件处理器（增强版）==========
class FileProcessor:
    def __init__(self, db):
        self.db = db
        if db is not None:
            self.name_list = set(db['姓名'].tolist())
            print(f"加载了 {len(self.name_list)} 个姓名")
        else:
            self.name_list = set()
    
    def find_dates_in_text(self, text):
        """在文本中查找日期，避免重复匹配"""
        date_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',  # 2025年2月20日 (优先匹配完整格式)
            r'\d{4}-\d{1,2}-\d{1,2}',       # 2025-02-20
            r'\d{4}/\d{1,2}/\d{1,2}',       # 2025/02/20
            r'\d{8}',                       # 20250220
            r'\d{1,2}月\d{1,2}日',          # 2月10日 (最后匹配，避免与完整格式冲突)
        ]

        dates = []
        used_positions = set()  # 记录已使用的位置，避免重复匹配

        for pattern in date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                start, end = match.start(), match.end()
                # 检查是否与已有匹配重叠
                if not any(pos in range(start, end) for pos in used_positions):
                    dates.append((start, end, match.group()))
                    # 标记这些位置为已使用
                    used_positions.update(range(start, end))

        # 按位置排序
        dates.sort(key=lambda x: x[0])
        return dates
    
    def find_encoded_dates_in_text(self, text):
        """在文本中查找已加密的日期，避免重复匹配"""
        patterns = [
            r'\d{4}年\d{1,2}月D_[A-Z]+日',    # 2025年2月D_XX日 (优先匹配完整格式)
            r'\d{1,2}月D_[A-Z]+日',           # 2月D_XX日 (避免与完整格式冲突)
        ]
        dates = []
        used_positions = set()  # 记录已使用的位置，避免重复匹配

        for pattern in patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                start, end = match.start(), match.end()
                # 检查是否与已有匹配重叠
                if not any(pos in range(start, end) for pos in used_positions):
                    dates.append((start, end, match.group()))
                    # 标记这些位置为已使用
                    used_positions.update(range(start, end))

        # 按位置排序
        dates.sort(key=lambda x: x[0])
        return dates
    
    def find_names_in_text(self, text):
        """在文本中查找姓名 - 贪婪最长匹配"""
        names = []
        i = 0
        
        while i < len(text):
            # 在当前位置寻找最长的匹配姓名
            longest_match = None
            longest_length = 0
            
            for name in self.name_list:
                if len(name) >= 2 and i + len(name) <= len(text):
                    if text[i:i + len(name)] == name and len(name) > longest_length:
                        longest_match = name
                        longest_length = len(name)
            
            if longest_match:
                names.append((i, i + longest_length, longest_match))
                i += longest_length  # 跳过已匹配的部分
            else:
                i += 1  # 移动到下一个字符
        
        return names
    
    def find_encoded_names_in_text(self, text):
        """在文本中查找已加密的姓名编码"""
        pattern = r'N_[0-9A-HJ-NP-Z]{4}'  # 更新：使用N_前缀
        codes = []
        matches = re.finditer(pattern, text)
        for match in matches:
            code = match.group()
            try:
                decode_employee_id(code)  # 验证是否能解码
                codes.append((match.start(), match.end(), code))
            except:
                continue
        return codes
    
    def process_text_content(self, text, encrypt=True):
        """处理文本内容，返回替换后的文本、变更记录和无效代码"""
        if not text or not text.strip():
            return text, [], []
        
        changes = []
        replacements = []
        invalid_codes = []
        
        if encrypt:
            # 加密模式
            # 查找日期
            dates = self.find_dates_in_text(text)
            for start, end, date_str in dates:
                try:
                    encrypted_date = encode_date_full(date_str)
                    replacements.append((start, end, date_str, encrypted_date))
                    changes.append(f"日期: {date_str} → {encrypted_date}")
                except Exception as e:
                    changes.append(f"日期加密失败: {date_str} ({str(e)})")
            
            # 查找姓名
            names = self.find_names_in_text(text)
            for start, end, name in names:
                try:
                    emp_id = name_to_empid(self.db, name)
                    encrypted_name = encode_employee_id(emp_id)
                    # 验证加密后的代码是否为6位
                    if len(encrypted_name) != 6:
                        invalid_codes.append(f"姓名 '{name}' 加密后代码长度异常: {encrypted_name} ({len(encrypted_name)}位)")
                    replacements.append((start, end, name, encrypted_name))
                    changes.append(f"姓名: {name} → {encrypted_name}")
                except Exception as e:
                    changes.append(f"姓名加密失败: {name} ({str(e)})")
        else:
            # 解密模式
            # 查找加密日期
            dates = self.find_encoded_dates_in_text(text)
            for start, end, date_str in dates:
                try:
                    decrypted_date = decode_date_full(date_str)
                    replacements.append((start, end, date_str, decrypted_date))
                    changes.append(f"日期: {date_str} → {decrypted_date}")
                except Exception as e:
                    changes.append(f"日期解密失败: {date_str} ({str(e)})")
            
            # 查找加密姓名
            codes = self.find_encoded_names_in_text(text)
            for start, end, code in codes:
                try:
                    if not code.startswith("N_"):
                        invalid_codes.append(f"发现无效前缀的人名代码: {code}")
                        continue
                    
                    code_part = code[2:]  # 去掉 "N_" 前缀
                    if len(code_part) != 4:
                        invalid_codes.append(f"发现非4位人名代码: {code} (编码部分{len(code_part)}位)")
                        continue
                    
                    emp_id = decode_employee_id(code)
                    info = empid_to_info(self.db, emp_id)
                    decrypted_name = info['姓名']
                    replacements.append((start, end, code, decrypted_name))
                    changes.append(f"姓名: {code} → {decrypted_name}")
                except Exception as e:
                    changes.append(f"姓名解密失败: {code} ({str(e)})")
                    invalid_codes.append(f"无效的人名代码: {code}")
        
        # 按位置倒序排列，从后往前替换
        replacements.sort(key=lambda x: x[0], reverse=True)
        
        # 执行替换
        new_text = text
        for start, end, original, replacement in replacements:
            new_text = new_text[:start] + replacement + new_text[end:]
        
        return new_text, changes, invalid_codes
    
    def process_word_document(self, input_path, output_path, encrypt=True):
        """处理Word文档，保持格式一致性"""
        try:
            if not input_path.lower().endswith('.docx'):
                return False, [f"不支持的Word文件格式: {input_path}"], []

            doc = Document(input_path)
            changes = []
            all_invalid_codes = []

            # 处理段落
            for paragraph in doc.paragraphs:
                changes_made, para_changes, invalid_codes = self.process_paragraph_with_format(paragraph, encrypt)
                if changes_made:
                    changes.extend(para_changes)
                all_invalid_codes.extend(invalid_codes)

            # 处理表格
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            changes_made, cell_changes, invalid_codes = self.process_paragraph_with_format(paragraph, encrypt)
                            if changes_made:
                                changes.extend(cell_changes)
                            all_invalid_codes.extend(invalid_codes)

            # 保存文档
            doc.save(output_path)
            return True, changes, all_invalid_codes

        except Exception as e:
            return False, [f"处理Word文档失败: {str(e)}"], []

    def process_paragraph_with_format(self, paragraph, encrypt=True):
        """处理段落并保持格式一致性"""
        if not paragraph.text.strip():
            return False, [], []

        original_text = paragraph.text
        new_text, changes, invalid_codes = self.process_text_content(original_text, encrypt)

        if new_text == original_text:
            return False, changes, invalid_codes

        # 保持格式的替换策略
        if len(paragraph.runs) == 1:
            # 单个run，直接替换
            paragraph.runs[0].text = new_text
        elif len(paragraph.runs) > 1:
            # 多个run，需要智能替换以保持格式
            self.replace_text_preserve_format(paragraph, original_text, new_text)
        else:
            # 没有run，直接设置文本
            paragraph.text = new_text

        return True, changes, invalid_codes

    def replace_text_preserve_format(self, paragraph, original_text, new_text):
        """在保持格式的情况下替换文本"""
        try:
            # 如果文本长度相近，尝试逐字符替换
            if abs(len(new_text) - len(original_text)) <= len(original_text) * 0.3:
                self.character_wise_replace(paragraph, original_text, new_text)
            else:
                # 长度差异较大，使用简单替换
                self.simple_replace_preserve_first_run_format(paragraph, new_text)
        except:
            # 如果出错，回退到简单替换
            self.simple_replace_preserve_first_run_format(paragraph, new_text)

    def character_wise_replace(self, paragraph, original_text, new_text):
        """逐字符替换，尽量保持原有格式"""
        runs = paragraph.runs
        if not runs:
            paragraph.text = new_text
            return

        # 构建字符到run的映射
        char_to_run = []
        current_pos = 0

        for run_idx, run in enumerate(runs):
            run_text = run.text
            for char in run_text:
                if current_pos < len(original_text):
                    char_to_run.append(run_idx)
                current_pos += 1

        # 清空所有run的文本
        for run in runs:
            run.text = ""

        # 将新文本分配到对应的run中
        for i, char in enumerate(new_text):
            if i < len(char_to_run):
                run_idx = char_to_run[i]
                runs[run_idx].text += char
            else:
                # 超出原有范围，添加到最后一个run
                if runs:
                    runs[-1].text += char

    def simple_replace_preserve_first_run_format(self, paragraph, new_text):
        """简单替换，保持第一个run的格式"""
        if not paragraph.runs:
            paragraph.text = new_text
            return

        # 保存第一个run的格式
        first_run = paragraph.runs[0]
        font = first_run.font

        # 清空所有run
        for run in paragraph.runs[1:]:
            run.text = ""

        # 设置新文本到第一个run
        first_run.text = new_text
    
    def process_excel_document(self, input_path, output_path, encrypt=True):
        """处理Excel文档（增强版）"""
        try:
            # 支持.xls和.xlsx格式
            if input_path.lower().endswith('.xlsx'):
                workbook = load_workbook(input_path)
            elif input_path.lower().endswith('.xls'):
                # 先用pandas读取.xls，然后转换为.xlsx格式处理
                df_dict = pd.read_excel(input_path, sheet_name=None)
                temp_xlsx = input_path.replace('.xls', '_temp.xlsx')
                with pd.ExcelWriter(temp_xlsx, engine='openpyxl') as writer:
                    for sheet_name, df in df_dict.items():
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                workbook = load_workbook(temp_xlsx)
                os.remove(temp_xlsx)  # 删除临时文件
            else:
                return False, [f"不支持的Excel文件格式: {input_path}"], []
            
            changes = []
            all_invalid_codes = []
            
            # 遍历所有工作表
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                
                # 遍历所有单元格
                for row in sheet.iter_rows():
                    for cell in row:
                        if cell.value is not None:
                            # 确保转换为字符串，处理各种数据类型
                            if isinstance(cell.value, (int, float)):
                                # 数字类型，检查是否为整数
                                if isinstance(cell.value, float) and cell.value.is_integer():
                                    original_text = str(int(cell.value))
                                else:
                                    original_text = str(cell.value)
                            elif isinstance(cell.value, str):
                                original_text = cell.value
                            else:
                                # 其他类型（日期等）
                                original_text = str(cell.value)
                            
                            # 只处理非空字符串
                            if original_text.strip():
                                new_text, cell_changes, invalid_codes = self.process_text_content(original_text, encrypt)
                                if new_text != original_text:
                                    cell.value = new_text
                                    changes.extend([f"[{sheet_name}] {change}" for change in cell_changes])
                                all_invalid_codes.extend(invalid_codes)
            
            # 保存为xlsx格式
            if output_path.lower().endswith('.xls'):
                output_path = output_path.replace('.xls', '.xlsx')
            
            workbook.save(output_path)
            return True, changes, all_invalid_codes
            
        except Exception as e:
            return False, [f"处理Excel文档失败: {str(e)}"], []

    
    def process_file(self, input_path, output_path, encrypt=True):
        """处理单个文件"""
        filename = os.path.basename(input_path)
        
        # 处理文件名
        if encrypt:
            new_filename = encrypt_filename(filename, self.db)
        else:
            new_filename = decrypt_filename(filename, self.db)
        
        # 更新输出路径
        output_dir = os.path.dirname(output_path)
        final_output_path = os.path.join(output_dir, new_filename)
        
        changes = []
        all_invalid_codes = []
        filename_changed = filename != new_filename
        
        if filename_changed:
            changes.append(f"文件名: {filename} → {new_filename}")
        
        # 根据文件类型处理内容
        if filename.lower().endswith('.docx'):
            # Word文档
            success, content_changes, invalid_codes = self.process_word_document(input_path, final_output_path, encrypt)
            changes.extend(content_changes)
            all_invalid_codes.extend(invalid_codes)
            return success, changes, new_filename, all_invalid_codes
            
        elif filename.lower().endswith(('.xlsx', '.xls')):
            # Excel文档
            success, content_changes, invalid_codes = self.process_excel_document(input_path, final_output_path, encrypt)
            changes.extend(content_changes)
            all_invalid_codes.extend(invalid_codes)
            return success, changes, new_filename, all_invalid_codes
            
        else:
            # 其他文件类型，只处理文件名
            try:
                shutil.copy2(input_path, final_output_path)
                return True, changes, new_filename, all_invalid_codes
            except Exception as e:
                return False, [f"复制文件失败: {str(e)}"], new_filename, all_invalid_codes

def clear_directory(directory):
    """清空目录内容"""
    if not os.path.exists(directory):
        return
    
    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)
        try:
            if os.path.isfile(item_path):
                os.remove(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)
        except Exception as e:
            print(f"删除 {item_path} 失败: {e}")

def copy_directory_structure(src, dst):
    """复制目录结构（不包含文件）"""
    for root, dirs, files in os.walk(src):
        # 计算相对路径
        rel_path = os.path.relpath(root, src)
        if rel_path == '.':
            continue
        
        # 创建对应的目录结构
        dst_dir = os.path.join(dst, rel_path)
        os.makedirs(dst_dir, exist_ok=True)

# ========== 欢迎对话框 ==========
class WelcomeDialog(tk.Toplevel):
    def __init__(self, parent, message, input_dir, file_count):
        super().__init__(parent)
        self.input_dir = input_dir
        self.file_count = file_count
        
        self.title("欢迎使用去识别化工具")
        self.geometry("600x500")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()
        
        # 居中显示
        self.center_window()
        
        self.init_ui(message)
        
        # 自动打开文件夹（延迟一点，让对话框先显示）
        self.after(500, self.open_folder)
    def center_window(self):
        """将窗口居中显示"""
        self.update_idletasks()
        
        # 获取屏幕尺寸
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # 获取窗口尺寸
        window_width = 600
        window_height = 500
        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
    def init_ui(self, message):
        # 创建主容器
        main_container = tk.Frame(self)
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题区域
        title_frame = tk.Frame(main_container)
        title_frame.pack(fill='x', pady=(0, 20))
        
        # 图标和标题
        title_label = tk.Label(title_frame, text="🚀 去识别化工具", 
                              font=("微软雅黑", 18, "bold"), fg="#2E7D32")
        title_label.pack()
        
        version_label = tk.Label(title_frame, text="v3.0 - 成都航空有限公司飞行技术管理部", 
                               font=("微软雅黑", 10), fg="gray")
        version_label.pack(pady=(5, 0))
        
        # 消息显示区域
        message_frame = tk.LabelFrame(main_container, text="系统信息", 
                                     font=("微软雅黑", 11, "bold"))
        message_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        message_content = tk.Frame(message_frame)
        message_content.pack(fill='both', expand=True, padx=15, pady=15)
        
        # 使用Text控件显示消息，支持滚动
        message_text = tk.Text(message_content, font=("微软雅黑", 10), 
                              wrap=tk.WORD, bg='#f8f9fa', relief='flat')
        message_scrollbar = tk.Scrollbar(message_content, orient="vertical", 
                                        command=message_text.yview)
        message_text.configure(yscrollcommand=message_scrollbar.set)
        
        message_text.pack(side="left", fill="both", expand=True)
        message_scrollbar.pack(side="right", fill="y")
        
        # 插入消息内容
        message_text.insert(1.0, message)
        message_text.config(state='disabled')  # 设为只读
        
        # 状态指示
        status_frame = tk.Frame(main_container)
        status_frame.pack(fill='x', pady=(0, 20))
        
        if self.file_count > 0:
            status_text = f"✅ 已发现 {self.file_count} 个待处理文件，可以开始处理"
            status_color = "green"
        else:
            status_text = "📂 待处理文件夹已打开，请放入需要处理的文件"
            status_color = "orange"
        
        status_label = tk.Label(status_frame, text=status_text, 
                               font=("微软雅黑", 11, "bold"), fg=status_color)
        status_label.pack()
        
        # 按钮区域
        button_frame = tk.Frame(main_container)
        button_frame.pack(fill='x')
        
        # 按钮样式
        button_style = {
            'font': ("微软雅黑", 10),
            'width': 15,
            'height': 1
        }
        
        folder_button_style = {
            'bg': '#E3F2FD',
            'fg': '#1565C0',
            'activebackground': '#BBDEFB',
            'activeforeground': '#0D47A1',
            **button_style
        }
        
        ok_button_style = {
            'bg': '#E8F5E8',
            'fg': '#2E7D32',
            'activebackground': '#C8E6C9',
            'activeforeground': '#1B5E20',
            **button_style
        }
        
        # 左侧按钮
        left_buttons = tk.Frame(button_frame)
        left_buttons.pack(side='left')
        
        folder_btn = tk.Button(left_buttons, text="📁 重新打开文件夹", 
                              command=self.open_folder, **folder_button_style)
        folder_btn.pack(side='left', padx=(0, 10))
        
        # 右侧按钮
        right_buttons = tk.Frame(button_frame)
        right_buttons.pack(side='right')
        
        ok_btn = tk.Button(right_buttons, text="✅ 开始使用", 
                          command=self.close_dialog, **ok_button_style)
        ok_btn.pack(side='left')
        
        # 绑定回车键
        self.bind('<Return>', lambda e: self.close_dialog())
        
        # 设置焦点
        ok_btn.focus_set()
    def open_folder(self):
        """打开待处理文件夹"""
        try:
            if sys.platform == "win32":
                os.startfile(self.input_dir)
            elif sys.platform == "darwin":  # macOS
                os.system(f"open '{self.input_dir}'")
            else:  # Linux
                os.system(f"xdg-open '{self.input_dir}'")
        except Exception as e:
            messagebox.showerror("打开失败", f"无法打开文件夹: {str(e)}")
    def close_dialog(self):
        """关闭对话框"""
        self.destroy()
        
# ========== 主界面 ==========
# ========== 主界面（修改版）==========
class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("去识别化工具 v3.0")
        self.geometry("900x800")
        self.resizable(False, False)
        self.db = None
        self.db_path = None
        
        # 初始化数据库
        self.init_database()
        
        # 确保工作目录存在并显示欢迎信息
        self.ensure_directories_and_show_welcome()
        
        # 初始化主界面
        self.init_main()
    def init_database(self):
        """初始化数据库"""
        try:
            self.db_path = get_database_path()
            
            if not os.path.exists(self.db_path):
                # 数据库文件不存在，提示用户选择
                error_msg = f"数据库文件不存在:\n{self.db_path}\n\n请选择数据库文件，或将'技术资格管理.xls'放到程序同目录下。"
                messagebox.showwarning("数据库文件缺失", error_msg)
                
                if messagebox.askyesno("选择数据库", "是否要手动选择数据库文件？"):
                    self.select_database_file()
                else:
                    self.quit()
                    return
            
            # 加载数据库
            self.db = load_employee_db(self.db_path)
            print(f"数据库加载成功: {self.db_path}")
            print(f"有效记录数: {len(self.db)}")
            
        except Exception as e:
            error_msg = f"数据库加载失败: {str(e)}\n\n请确保数据库文件格式正确，包含必要的列：姓名、员工号、有效性"
            messagebox.showerror("数据库加载失败", error_msg)
            
            if messagebox.askyesno("重新选择", "是否要重新选择数据库文件？"):
                self.select_database_file()
            else:
                self.quit()
    def select_database_file(self):
        """手动选择数据库文件"""
        file_path = filedialog.askopenfilename(
            title="选择员工数据库文件",
            filetypes=[("Excel文件", "*.xls *.xlsx"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                # 测试文件
                test_df = pd.read_excel(file_path)
                required_columns = ['姓名', '员工号', '有效性']
                missing_columns = [col for col in required_columns if col not in test_df.columns]
                
                if missing_columns:
                    messagebox.showerror("文件格式错误", 
                                       f"所选文件缺少必要的列: {', '.join(missing_columns)}")
                    self.quit()
                    return
                
                # 复制到程序目录
                target_path = get_database_path()
                shutil.copy2(file_path, target_path)
                
                # 重新加载
                self.db_path = target_path
                self.db = load_employee_db(self.db_path)
                
                messagebox.showinfo("成功", f"数据库文件已设置为:\n{target_path}")
                print(f"数据库加载成功: {file_path}")
                
            except Exception as e:
                messagebox.showerror("加载失败", f"无法加载所选文件: {str(e)}")
                self.quit()
        else:
            self.quit()
    def ensure_directories_and_show_welcome(self):
        """确保工作目录存在并显示欢迎信息"""
        try:
            # 确保工作目录存在
            input_dir, output_dir, log_dir = ensure_work_directories()
            
            # 延迟执行，确保主窗口已经显示
            self.after(1000, lambda: self.show_welcome_and_open_folder(input_dir, output_dir, log_dir))
            
        except Exception as e:
            messagebox.showerror("目录创建失败", f"无法创建工作目录: {str(e)}")
    def show_welcome_and_open_folder(self, input_dir, output_dir, log_dir):
        """显示欢迎信息并打开文件夹"""
        try:
            # 统计现有文件
            file_count = self.count_files_in_directory(input_dir)
            
            # 构建欢迎消息
            welcome_msg = "欢迎使用去识别化工具 v3.0！\n\n"
            welcome_msg += "📁 工作文件夹已准备就绪：\n"
            welcome_msg += f"• 待处理文件夹: 已创建\n"
            welcome_msg += f"• 处理后文件夹: 已创建\n"
            welcome_msg += f"• 日志文件夹: 已创建\n\n"
            
            if file_count['total'] > 0:
                welcome_msg += f"📊 当前待处理文件统计：\n"
                welcome_msg += f"• 总文件数: {file_count['total']} 个\n"
                welcome_msg += f"• Word文档: {file_count['word']} 个\n"
                welcome_msg += f"• Excel表格: {file_count['excel']} 个\n"
                welcome_msg += f"• 其他文件: {file_count['other']} 个\n\n"
                
                # 检查.doc文件
                doc_files = check_for_doc_files(input_dir)
                if doc_files:
                    welcome_msg += f"⚠️ 发现 {len(doc_files)} 个.doc文件需要转换\n\n"
                
                welcome_msg += "✅ 发现待处理文件，您可以直接开始处理！"
            else:
                welcome_msg += "📝 使用说明：\n"
                welcome_msg += "1. 将需要加密/解密的文件放入「待处理文件夹」\n"
                welcome_msg += "2. 支持Word(.docx)、Excel(.xlsx/.xls)等格式\n"
                welcome_msg += "3. 放入文件后，点击「一键加密」或「一键解密」\n"
                welcome_msg += "4. 处理完成的文件将保存在「处理后文件夹」\n\n"
                welcome_msg += "🔧 现在将为您打开「待处理文件夹」，请放入需要处理的文件。"
            
            # 显示欢迎对话框
            welcome_dialog = WelcomeDialog(self, welcome_msg, input_dir, file_count['total'])
            self.wait_window(welcome_dialog)
            
        except Exception as e:
            # 如果出错，至少尝试打开文件夹
            self.open_input_folder_silent(input_dir)
    def open_input_folder_silent(self, input_dir):
        """静默打开输入文件夹"""
        try:
            if sys.platform == "win32":
                os.startfile(input_dir)
            elif sys.platform == "darwin":  # macOS
                os.system(f"open '{input_dir}'")
            else:  # Linux
                os.system(f"xdg-open '{input_dir}'")
        except Exception as e:
            print(f"无法打开文件夹: {e}")

    def confirm_database_version(self, operation_name):
        """确认数据库版本"""
        dialog = DatabaseConfirmDialog(self, self.db_path, operation_name)
        self.wait_window(dialog)
        
        if dialog.result:
            # 用户确认，重新加载数据库（可能已更新）
            try:
                self.db = load_employee_db(self.db_path)
                return True
            except Exception as e:
                messagebox.showerror("数据库错误", f"重新加载数据库失败: {str(e)}")
                return False
        else:
            # 用户取消
            return False
    def init_main(self):
        # 清空所有子控件
        for widget in self.winfo_children():
            widget.destroy()
        
        # 确保工作目录存在
        input_dir, output_dir, log_dir = ensure_work_directories()
        
        # 创建主框架
        main_frame = tk.Frame(self)
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # 标题区域
        title_frame = tk.Frame(main_frame)
        title_frame.pack(pady=(0, 20))
        
        title_label = tk.Label(title_frame, text="去识别化工具", font=("微软雅黑", 20, "bold"))
        title_label.pack()
        
        version_label = tk.Label(title_frame, text="v3.0 ©成都航空有限公司飞行技术管理部", 
                               font=("微软雅黑", 10), fg="gray")
        version_label.pack(pady=(5, 0))
        
        # 数据库状态显示
        db_status_frame = tk.LabelFrame(main_frame, text="数据库状态", font=("微软雅黑", 11, "bold"))
        db_status_frame.pack(fill='x', pady=(0, 20))
        
        db_info_frame = tk.Frame(db_status_frame)
        db_info_frame.pack(padx=15, pady=10)
        
        if self.db is not None:
            db_info = f"✓ 数据库已加载: {len(self.db)}条有效记录"
            status_color = "green"
            db_time = get_database_version(self.db_path)
            tk.Label(db_info_frame, text=db_info, font=("微软雅黑", 10, "bold"), fg=status_color).pack()
            tk.Label(db_info_frame, text=f"最后更新: {db_time}", font=("微软雅黑", 9), fg="gray").pack()
        else:
            db_info = "✗ 数据库未加载"
            status_color = "red"
            tk.Label(db_info_frame, text=db_info, font=("微软雅黑", 10, "bold"), fg=status_color).pack()
        
        # 工作目录信息
        dir_info_frame = tk.LabelFrame(main_frame, text="工作目录", font=("微软雅黑", 11, "bold"))
        dir_info_frame.pack(fill='x', pady=(0, 20))
        
        dir_content = tk.Frame(dir_info_frame)
        dir_content.pack(padx=15, pady=10)
        
        # 输入目录信息
        input_info_frame = tk.Frame(dir_content)
        input_info_frame.pack(fill='x', pady=2)
        
        tk.Label(input_info_frame, text="待处理文件夹:", font=("微软雅黑", 9, "bold")).pack(side='left')
        tk.Label(input_info_frame, text=input_dir, font=("微软雅黑", 8), fg="blue").pack(side='left', padx=(5, 0))
        
        # 输出目录信息
        output_info_frame = tk.Frame(dir_content)
        output_info_frame.pack(fill='x', pady=2)
        
        tk.Label(output_info_frame, text="处理后文件夹:", font=("微软雅黑", 9, "bold")).pack(side='left')
        tk.Label(output_info_frame, text=output_dir, font=("微软雅黑", 8), fg="green").pack(side='left', padx=(5, 0))
        
        # 日志目录信息
        log_info_frame = tk.Frame(dir_content)
        log_info_frame.pack(fill='x', pady=2)
        
        tk.Label(log_info_frame, text="日志文件夹:", font=("微软雅黑", 9, "bold")).pack(side='left')
        tk.Label(log_info_frame, text=log_dir, font=("微软雅黑", 8), fg="purple").pack(side='left', padx=(5, 0))
        
        # 文件统计
        stats_frame = tk.Frame(dir_content)
        stats_frame.pack(fill='x', pady=(10, 0))
        
        file_count = self.count_files_in_directory(input_dir)
        stats_text = f"待处理文件: {file_count['total']}个 (Word:{file_count['word']}, Excel:{file_count['excel']}, 其他:{file_count['other']})"
        
        # 检查是否有.doc文件
        doc_files = check_for_doc_files(input_dir)
        if doc_files:
            stats_text += f" ⚠️发现{len(doc_files)}个.doc文件"
        
        tk.Label(stats_frame, text=stats_text, font=("微软雅黑", 9), fg="gray").pack()
        
        # 主要功能按钮区域
        main_buttons_frame = tk.LabelFrame(main_frame, text="一键处理", font=("微软雅黑", 12, "bold"))
        main_buttons_frame.pack(fill='x', pady=(0, 20))
        
        buttons_container = tk.Frame(main_buttons_frame)
        buttons_container.pack(pady=20)
        
        # 按钮样式
        encrypt_button_style = {
            'bg': '#E8F5E8',           # 淡绿色背景
            'fg': '#2E7D32',           # 深绿色文字
            'relief': 'raised',
            'bd': 3,
            'activebackground': '#C8E6C9',
            'activeforeground': '#1B5E20',
            'font': ("微软雅黑", 14, "bold"),
            'width': 15,
            'height': 2
        }
        
        decrypt_button_style = {
            'bg': '#E3F2FD',           # 淡蓝色背景
            'fg': '#1565C0',           # 深蓝色文字
            'relief': 'raised',
            'bd': 3,
            'activebackground': '#BBDEFB',
            'activeforeground': '#0D47A1',
            'font': ("微软雅黑", 14, "bold"),
            'width': 15,
            'height': 2
        }
        
        # 加密按钮
        encrypt_btn = tk.Button(
            buttons_container,
            text="一键加密",
            command=self.start_encrypt_all,
            **encrypt_button_style
        )
        encrypt_btn.pack(side='left', padx=30)
        
        # 解密按钮
        decrypt_btn = tk.Button(
            buttons_container,
            text="一键解密",
            command=self.start_decrypt_all,
            **decrypt_button_style
        )
        decrypt_btn.pack(side='left', padx=30)
        
        # 辅助功能区域
        aux_frame = tk.LabelFrame(main_frame, text="辅助功能", font=("微软雅黑", 11, "bold"))
        aux_frame.pack(fill='x', pady=(0, 20))
        
        aux_buttons = tk.Frame(aux_frame)
        aux_buttons.pack(pady=15)
        # 辅助按钮样式
        aux_button_style = {
            'bg': '#F5F5F5',
            'fg': '#424242',
            'relief': 'raised',
            'bd': 1,
            'activebackground': '#E0E0E0',
            'activeforeground': '#212121',
            'font': ("微软雅黑", 10),
            'width': 12
        }
        
        tk.Button(aux_buttons, text="单项查询", command=self.goto_single_query, **aux_button_style).pack(side='left', padx=10)
        tk.Button(aux_buttons, text="刷新文件列表", command=self.refresh_file_count, **aux_button_style).pack(side='left', padx=10)
        tk.Button(aux_buttons, text="打开文件夹", command=self.open_folders, **aux_button_style).pack(side='left', padx=10)
        tk.Button(aux_buttons, text="更新数据库", command=self.update_database, **aux_button_style).pack(side='left', padx=10)
        tk.Button(aux_buttons, text="测试特定姓名", command=self.test_specific_names, **aux_button_style).pack(side='left', padx=10)
        
        # 使用说明区域
        info_frame = tk.LabelFrame(main_frame, text="使用说明", font=("微软雅黑", 11, "bold"))
        info_frame.pack(fill='both', expand=True)
        
        info_content = tk.Frame(info_frame)
        info_content.pack(padx=15, pady=10, fill='both', expand=True)
        
        instructions = [
            "1. 将需要处理的文件放入「待处理文件夹」",
            "2. Word和Excel文件会处理内容和文件名，其他文件只处理文件名",
            "3. 点击「一键加密」或「一键解密」开始处理",
            "4. 处理完成的文件会保存到「处理后文件夹」",
            "5. 每次处理前会自动清空「处理后文件夹」",
            "6. 完整的处理日志会自动保存到「log文件夹」",
            "",
            "支持的文件格式:",
            "• Word文档: .docx (推荐)",
            "• Excel表格: .xlsx, .xls", 
            "• 其他文件: 仅处理文件名",
            "",
            "⚠️ 重要提示:",
            "• .doc文件将被跳过，需手动转换为.docx格式",
            "• 人名代码必须为4位，异常情况会有提醒",
            "• 详细的处理日志可在log文件夹中查看"
        ]
        
        for instruction in instructions:
            if instruction == "":
                tk.Label(info_content, text="", font=("微软雅黑", 8)).pack(anchor='w')
            elif instruction.startswith("支持的文件格式:") or instruction.startswith("⚠️ 重要提示:"):
                tk.Label(info_content, text=instruction, font=("微软雅黑", 9, "bold"), fg="#FF6600").pack(anchor='w', pady=(5, 2))
            elif instruction.startswith("•"):
                tk.Label(info_content, text=instruction, font=("微软雅黑", 8), fg="gray").pack(anchor='w', padx=(10, 0))
            else:
                tk.Label(info_content, text=instruction, font=("微软雅黑", 9)).pack(anchor='w', pady=1)
    def count_files_in_directory(self, directory):
        """统计目录中的文件数量"""
        if not os.path.exists(directory):
            return {'total': 0, 'word': 0, 'excel': 0, 'other': 0}
        
        counts = {'total': 0, 'word': 0, 'excel': 0, 'other': 0}
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                counts['total'] += 1
                if file.lower().endswith('.docx'):
                    counts['word'] += 1
                elif file.lower().endswith(('.xlsx', '.xls')):
                    counts['excel'] += 1
                else:
                    counts['other'] += 1
        
        return counts
    def refresh_file_count(self):
        """刷新文件统计"""
        input_dir, output_dir, log_dir = get_work_directories()
        file_count = self.count_files_in_directory(input_dir)
        
        # 显示刷新结果
        if file_count['total'] > 0:
            msg = f"📊 文件统计已刷新：\n\n"
            msg += f"总文件数: {file_count['total']} 个\n"
            msg += f"Word文档: {file_count['word']} 个\n"
            msg += f"Excel表格: {file_count['excel']} 个\n"
            msg += f"其他文件: {file_count['other']} 个\n"
            
            # 检查.doc文件
            doc_files = check_for_doc_files(input_dir)
            if doc_files:
                msg += f"\n⚠️ 发现 {len(doc_files)} 个.doc文件需要转换"
            
            messagebox.showinfo("文件统计", msg)
        else:
            msg = "📂 待处理文件夹中暂无文件\n\n"
            msg += "是否要打开文件夹添加文件？"
            if messagebox.askyesno("文件统计", msg):
                self.open_folders()
        
        # 刷新界面
        self.init_main()
    def open_folders(self):
        """打开工作文件夹"""
        input_dir, output_dir, log_dir = get_work_directories()
        
        # 根据操作系统打开文件夹
        try:
            if sys.platform == "win32":
                os.startfile(input_dir)
            elif sys.platform == "darwin":  # macOS
                os.system(f"open '{input_dir}'")
            else:  # Linux
                os.system(f"xdg-open '{input_dir}'")
        except Exception as e:
            messagebox.showerror("打开失败", f"无法打开文件夹: {str(e)}")
    def update_database(self):
        """更新数据库"""
        dialog = DatabaseConfirmDialog(self, self.db_path, "数据库管理")
        self.wait_window(dialog)
        
        # 刷新主界面显示
        self.init_main()

    def goto_single_query(self):
        """打开单项查询窗口"""
        if not self.confirm_database_version("单项查询"):
            return
        self.withdraw()
        SingleQueryWindow(self, self.db)
    def start_encrypt_all(self):
        """开始一键加密"""
        if not self.confirm_database_version("一键加密"):
            return
        
        input_dir, output_dir, log_dir = get_work_directories()
        
        # 检查是否有.doc文件
        doc_files = check_for_doc_files(input_dir)
        if doc_files:
            doc_list = '\n'.join(doc_files[:10])  # 最多显示10个
            if len(doc_files) > 10:
                doc_list += f'\n... 还有{len(doc_files)-10}个文件'
            
            msg = f"发现 {len(doc_files)} 个.doc文件，这些文件将被跳过：\n\n{doc_list}\n\n"
            msg += "建议先将.doc文件转换为.docx格式。\n是否继续处理其他文件？"
            
            if not messagebox.askyesno("发现.doc文件", msg):
                return
        
        # 检查是否有文件需要处理
        file_count = self.count_files_in_directory(input_dir)
        if file_count['total'] == 0:
            messagebox.showwarning("提示", "待处理文件夹中没有文件")
            return
        
        # 确认处理
        msg = f"即将加密处理 {file_count['total']} 个文件\n\n"
        msg += f"Word文档: {file_count['word']} 个\n"
        msg += f"Excel表格: {file_count['excel']} 个\n"
        msg += f"其他文件: {file_count['other']} 个\n\n"
        if doc_files:
            msg += f"⚠️ 将跳过 {len(doc_files)} 个.doc文件\n\n"
        msg += "处理后文件夹将被清空，确定继续吗？"
        
        if not messagebox.askyesno("确认加密", msg):
            return
        
        # 打开处理窗口
        self.withdraw()
        ProcessWindow(self, self.db, encrypt=True)
    def start_decrypt_all(self):
        """开始一键解密"""
        if not self.confirm_database_version("一键解密"):
            return
        
        input_dir, output_dir, log_dir = get_work_directories()
        
        # 检查是否有.doc文件
        doc_files = check_for_doc_files(input_dir)
        if doc_files:
            doc_list = '\n'.join(doc_files[:10])  # 最多显示10个
            if len(doc_files) > 10:
                doc_list += f'\n... 还有{len(doc_files)-10}个文件'
            
            msg = f"发现 {len(doc_files)} 个.doc文件，这些文件将被跳过：\n\n{doc_list}\n\n"
            msg += "建议先将.doc文件转换为.docx格式。\n是否继续处理其他文件？"
            
            if not messagebox.askyesno("发现.doc文件", msg):
                return
        
        # 检查是否有文件需要处理
        file_count = self.count_files_in_directory(input_dir)
        if file_count['total'] == 0:
            messagebox.showwarning("提示", "待处理文件夹中没有文件")
            return
        
        # 确认处理
        msg = f"即将解密处理 {file_count['total']} 个文件\n\n"
        msg += f"Word文档: {file_count['word']} 个\n"
        msg += f"Excel表格: {file_count['excel']} 个\n"
        msg += f"其他文件: {file_count['other']} 个\n\n"
        if doc_files:
            msg += f"⚠️ 将跳过 {len(doc_files)} 个.doc文件\n\n"
        msg += "处理后文件夹将被清空，确定继续吗？"
        
        if not messagebox.askyesno("确认解密", msg):
            return
        
        # 打开处理窗口
        self.withdraw()
        ProcessWindow(self, self.db, encrypt=False)
    def test_specific_names(self):
        """测试特定姓名的加密结果"""
        if not self.db is not None:
            messagebox.showerror("错误", "数据库未加载")
            return
        
        # 创建测试窗口
        test_window = tk.Toplevel(self)
        test_window.title("特定姓名加密测试")
        test_window.geometry("800x600")
        test_window.resizable(True, True)
        
        # 标题
        title_label = tk.Label(test_window, text="特定姓名加密测试", 
                            font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=10)
        
        # 输入区域
        input_frame = tk.LabelFrame(test_window, text="输入姓名", font=("微软雅黑", 11, "bold"))
        input_frame.pack(fill='x', padx=20, pady=10)
        
        input_content = tk.Frame(input_frame)
        input_content.pack(padx=15, pady=15)
        
        tk.Label(input_content, text="姓名:", font=("微软雅黑", 10)).pack(side='left')
        name_var = tk.StringVar()
        name_entry = tk.Entry(input_content, textvariable=name_var, width=20, font=("微软雅黑", 10))
        name_entry.pack(side='left', padx=10)
        
        def test_single_name():
            name = name_var.get().strip()
            if not name:
                messagebox.showwarning("提示", "请输入姓名")
                return
            
            result_text.delete(1.0, tk.END)
            
            try:
                # 详细的测试过程
                result_text.insert(tk.END, f"=== 测试姓名: '{name}' ===\n\n")
                
                # 1. 检查数据库中是否存在
                matching_rows = self.db[self.db['姓名'] == name]
                if matching_rows.empty:
                    result_text.insert(tk.END, f"❌ 数据库中未找到姓名: '{name}'\n")
                    result_text.insert(tk.END, "可能的原因:\n")
                    result_text.insert(tk.END, "- 姓名不存在于数据库中\n")
                    result_text.insert(tk.END, "- 姓名包含特殊字符或空格\n")
                    result_text.insert(tk.END, "- 数据类型不匹配\n\n")
                    
                    # 尝试模糊匹配
                    similar_names = self.db[self.db['姓名'].str.contains(name, na=False)]
                    if not similar_names.empty:
                        result_text.insert(tk.END, "相似的姓名:\n")
                        for idx, row in similar_names.head(5).iterrows():
                            result_text.insert(tk.END, f"- '{row['姓名']}' (工号: {row['员工号']})\n")
                    return
                
                # 2. 显示匹配的记录
                result_text.insert(tk.END, f"✅ 在数据库中找到 {len(matching_rows)} 条记录:\n")
                for idx, row in matching_rows.iterrows():
                    result_text.insert(tk.END, f"- 姓名: '{row['姓名']}', 工号: {row['员工号']}, 型别: {row['型别']}\n")
                result_text.insert(tk.END, "\n")
                
                # 3. 使用第一条记录进行加密测试
                first_row = matching_rows.iloc[0]
                emp_id = int(first_row['员工号'])
                
                result_text.insert(tk.END, f"使用工号 {emp_id} 进行加密测试:\n")
                result_text.insert(tk.END, f"原始工号: {emp_id}\n")
                
                # 4. 详细的加密过程
                try:
                    # 加密步骤
                    offset = 15000
                    step1 = emp_id + offset
                    result_text.insert(tk.END, f"步骤1 - 加偏移量: {emp_id} + {offset} = {step1}\n")
                    
                    step2 = step1 * 9
                    result_text.insert(tk.END, f"步骤2 - 乘以9: {step1} * 9 = {step2}\n")
                    
                    # 转换为34进制
                    CHARSET = '0123456789ABCDEFGHJKLMNPQRSTUVWXYZ'
                    digits = []
                    num = step2
                    while num:
                        digits.append(num % 34)
                        num //= 34
                    digits = digits[::-1]
                    
                    result_text.insert(tk.END, f"步骤3 - 34进制数字: {digits}\n")
                    
                    # 补齐到4位
                    while len(digits) < 4:
                        digits = [0] + digits
                    result_text.insert(tk.END, f"步骤4 - 补齐4位: {digits}\n")
                    
                    # 5. 加密结果
                    encoded = ''.join(CHARSET[d] for d in digits)
                    reversed_encoded = encoded[::-1]
                    encrypted_code = "N_" + reversed_encoded
                    result_text.insert(tk.END, f"步骤5 - 加密结果: {encrypted_code}\n")
                    
                    # 6. 解密验证
                    try:
                        decoded_id = decode_employee_id(encrypted_code)
                        if decoded_id == emp_id:
                            result_text.insert(tk.END, f"解密验证成功: {encrypted_code} → {decoded_id}\n")
                        else:
                            result_text.insert(tk.END, f"解密验证失败: {encrypted_code} → {decoded_id} (预期: {emp_id})\n")
                    except Exception as e:
                        result_text.insert(tk.END, f"解密验证失败: {str(e)}\n")
                except Exception as e:
                    result_text.insert(tk.END, f"加密过程失败: {str(e)}\n")
            except Exception as e:
                result_text.insert(tk.END, f"测试失败: {str(e)}\n")
        
        # 测试按钮
        test_button = tk.Button(input_content, text="测试", command=test_single_name, font=("微软雅黑", 10))
        test_button.pack(side='left', padx=10)
        
        # 结果显示区域
        result_frame = tk.LabelFrame(test_window, text="测试结果", font=("微软雅黑", 11, "bold"))
        result_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        result_content = tk.Frame(result_frame)
        result_content.pack(padx=15, pady=15, fill='both', expand=True)
        
        result_text = tk.Text(result_content, font=("微软雅黑", 10), wrap=tk.WORD)
        result_text.pack(side='left', fill='both', expand=True)
        
        result_scrollbar = tk.Scrollbar(result_content, orient='vertical', command=result_text.yview)
        result_scrollbar.pack(side='right', fill='y')
        result_text.configure(yscrollcommand=result_scrollbar.set)
        
        def test_problem_names():
            """测试问题姓名"""
            problem_names = ["张雨豪", "张浩1"]
            result_text.delete(1.0, tk.END)
            
            for name in problem_names:
                name_var.set(name)
                test_single_name()
                result_text.insert(tk.END, "\n" + "="*50 + "\n\n")
        
        tk.Button(input_content, text="测试", command=test_single_name, 
                font=("微软雅黑", 10)).pack(side='left', padx=5)
        tk.Button(input_content, text="测试问题姓名", command=test_problem_names, 
                font=("微软雅黑", 10)).pack(side='left', padx=5)
        
        # 结果显示区域
        result_frame = tk.LabelFrame(test_window, text="测试结果", font=("微软雅黑", 11, "bold"))
        result_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        result_content = tk.Frame(result_frame)
        result_content.pack(fill='both', expand=True, padx=10, pady=10)
        
        result_text = tk.Text(result_content, font=("Consolas", 9))
        result_scrollbar = tk.Scrollbar(result_content, orient="vertical", command=result_text.yview)
        result_text.configure(yscrollcommand=result_scrollbar.set)
        
        result_text.pack(side="left", fill="both", expand=True)
        result_scrollbar.pack(side="right", fill="y")
        
        # 预设问题姓名
        name_var.set("张雨豪")
        
        # 关闭按钮
        tk.Button(test_window, text="关闭", command=test_window.destroy, 
                font=("微软雅黑", 10)).pack(pady=10)


# ========== 处理窗口（增强版，支持完整日志导出）==========
class ProcessWindow(tk.Toplevel):
    def __init__(self, master, db, encrypt=True):
        super().__init__(master)
        self.db = db
        self.processor = FileProcessor(db)
        self.encrypt = encrypt
        self.processing = False
        self.skipped_doc_files = []
        self.all_invalid_codes = []
        self.log_entries = []  # 存储所有日志条目
        
        operation = "加密" if encrypt else "解密"
        self.title(f"一键{operation}处理")
        self.geometry("1000x700")
        self.resizable(True, True)
        self.protocol("WM_DELETE_WINDOW", self.on_close)
        
        self.input_dir, self.output_dir, self.log_dir = get_work_directories()
        
        # 生成日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_filename = f"{operation}处理日志_{timestamp}.txt"
        self.log_filepath = os.path.join(self.log_dir, self.log_filename)
        
        self.init_ui()
        
        # 自动开始处理
        self.after(500, self.start_processing)

    def init_ui(self):
        operation = "加密" if self.encrypt else "解密"
        
        # 标题区域
        title_frame = tk.Frame(self)
        title_frame.pack(fill='x', padx=20, pady=10)
        
        title_label = tk.Label(title_frame, text=f"一键{operation}处理", 
                              font=("微软雅黑", 16, "bold"))
        title_label.pack()
        
        # 日志文件信息
        log_info_label = tk.Label(title_frame, text=f"日志文件: {self.log_filename}", 
                                 font=("微软雅黑", 9), fg="gray")
        log_info_label.pack(pady=(5, 0))
        
        # 进度信息区域
        progress_frame = tk.LabelFrame(self, text="处理进度", font=("微软雅黑", 11, "bold"))
        progress_frame.pack(fill='x', padx=20, pady=10)
        
        progress_content = tk.Frame(progress_frame)
        progress_content.pack(fill='x', padx=15, pady=10)
        
        # 当前处理文件
        self.current_file_var = tk.StringVar(value="准备开始...")
        tk.Label(progress_content, text="当前文件:", font=("微软雅黑", 10, "bold")).pack(anchor='w')
        tk.Label(progress_content, textvariable=self.current_file_var, 
                font=("微软雅黑", 9), fg="blue").pack(anchor='w', padx=(10, 0))
        
        # 进度条
        self.progress_bar = ttk.Progressbar(progress_content, mode='determinate')
        self.progress_bar.pack(fill='x', pady=(10, 5))
        
        # 进度文字
        self.progress_text_var = tk.StringVar(value="0/0")
        tk.Label(progress_content, textvariable=self.progress_text_var, 
                font=("微软雅黑", 10)).pack()
        
        # 处理日志区域
        log_frame = tk.LabelFrame(self, text="处理日志", font=("微软雅黑", 11, "bold"))
        log_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        log_content = tk.Frame(log_frame)
        log_content.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 日志文本框
        self.log_text = tk.Text(log_content, font=("微软雅黑", 9))
        log_scrollbar = tk.Scrollbar(log_content, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")
        
        # 按钮区域
        button_frame = tk.Frame(self)
        button_frame.pack(fill='x', padx=20, pady=10)
        
        # 按钮样式
        button_style = {
            'font': ("微软雅黑", 10),
            'width': 12
        }
        
        stop_style = {
            'bg': '#FFEBEE',
            'fg': '#C62828',
            'activebackground': '#FFCDD2',
            'activeforeground': '#B71C1C',
            **button_style
        }
        
        normal_style = {
            'bg': '#F5F5F5',
            'fg': '#424242',
            'activebackground': '#E0E0E0',
            'activeforeground': '#212121',
            **button_style
        }
        
        export_style = {
            'bg': '#E8F5E8',
            'fg': '#2E7D32',
            'activebackground': '#C8E6C9',
            'activeforeground': '#1B5E20',
            **button_style
        }
        
        self.stop_btn = tk.Button(button_frame, text="停止处理", 
                                 command=self.stop_processing, **stop_style)
        self.stop_btn.pack(side='left', padx=5)
        
        tk.Button(button_frame, text="清空日志", 
                 command=self.clear_log, **normal_style).pack(side='left', padx=5)
        
        self.export_btn = tk.Button(button_frame, text="导出完整日志", 
                                   command=self.export_full_log, **export_style)
        self.export_btn.pack(side='left', padx=5)
        
        tk.Button(button_frame, text="打开日志文件夹", 
                 command=self.open_log_folder, **normal_style).pack(side='left', padx=5)
        
        self.close_btn = tk.Button(button_frame, text="关闭窗口", 
                                  command=self.on_close, state='disabled', **normal_style)
        self.close_btn.pack(side='right', padx=5)

    def log(self, message, level="INFO", write_to_file=True):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if level == "ERROR":
            prefix = "✗"
            color = "red"
        elif level == "SUCCESS":
            prefix = "✓"
            color = "green"
        elif level == "WARNING":
            prefix = "⚠"
            color = "orange"
        elif level == "SKIP":
            prefix = "⏭"
            color = "blue"
        else:
            prefix = "•"
            color = "black"
        
        log_message = f"[{timestamp}] {prefix} {message}"
        display_message = log_message + "\n"
        
        # 存储到日志条目列表
        if write_to_file:
            self.log_entries.append({
                'timestamp': datetime.now(),
                'level': level,
                'message': message,
                'formatted': log_message
            })
        
        # 显示在界面上
        self.log_text.insert(tk.END, display_message)
        
        # 设置颜色（简单实现）
        if color != "black":
            start_line = self.log_text.index(tk.END + "-2l")
            end_line = self.log_text.index(tk.END + "-1l")
            self.log_text.tag_add(level, start_line, end_line)
            self.log_text.tag_config(level, foreground=color)
        
        self.log_text.see(tk.END)
        self.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def export_full_log(self):
        """导出完整日志到文件"""
        try:
            self.write_log_to_file()
            messagebox.showinfo("导出成功", f"完整日志已保存到:\n{self.log_filepath}")
        except Exception as e:
            messagebox.showerror("导出失败", f"无法导出日志: {str(e)}")

    def write_log_to_file(self):
        """将日志写入文件"""
        try:
            with open(self.log_filepath, 'w', encoding='utf-8') as f:
                # 写入文件头
                operation = "加密" if self.encrypt else "解密"
                f.write(f"去识别化工具 - {operation}处理日志\n")
                f.write(f"{'='*50}\n")
                f.write(f"处理时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
                f.write(f"数据库文件: {self.db.shape[0] if self.db is not None else 0}条有效记录\n")
                f.write(f"输入目录: {self.input_dir}\n")
                f.write(f"输出目录: {self.output_dir}\n")
                f.write(f"{'='*50}\n\n")
                
                # 写入所有日志条目
                for entry in self.log_entries:
                    f.write(f"{entry['formatted']}\n")
                
                # 写入文件尾
                f.write(f"\n{'='*50}\n")
                f.write(f"日志生成完成: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n")
                
        except Exception as e:
            raise Exception(f"写入日志文件失败: {str(e)}")

    def open_log_folder(self):
        """打开日志文件夹"""
        try:
            if sys.platform == "win32":
                os.startfile(self.log_dir)
            elif sys.platform == "darwin":  # macOS
                os.system(f"open '{self.log_dir}'")
            else:  # Linux
                os.system(f"xdg-open '{self.log_dir}'")
        except Exception as e:
            messagebox.showerror("打开失败", f"无法打开日志文件夹: {str(e)}")

    def start_processing(self):
        """开始处理"""
        if self.processing:
            return
        
        operation = "加密" if self.encrypt else "解密"
        self.log(f"开始{operation}处理", "INFO")
        self.log(f"数据库记录数: {len(self.db)}", "INFO")
        self.log(f"输入目录: {self.input_dir}", "INFO")
        self.log(f"输出目录: {self.output_dir}", "INFO")
        
        # 清空输出目录
        self.log("正在清空处理后文件夹...", "INFO")
        try:
            clear_directory(self.output_dir)
            self.log("处理后文件夹已清空", "SUCCESS")
        except Exception as e:
            self.log(f"清空处理后文件夹失败: {str(e)}", "ERROR")
            return
        
        # 获取所有需要处理的文件
        files_to_process = []
        for root, dirs, files in os.walk(self.input_dir):
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, self.input_dir)
                
                # 检查是否为.doc文件
                if file.lower().endswith('.doc') and not file.lower().endswith('.docx'):
                    self.skipped_doc_files.append(rel_path)
                    continue
                
                files_to_process.append((file_path, rel_path))
        
        if self.skipped_doc_files:
            self.log(f"跳过 {len(self.skipped_doc_files)} 个.doc文件", "SKIP")
            for doc_file in self.skipped_doc_files:
                self.log(f"跳过.doc文件: {doc_file}", "SKIP")
        
        if not files_to_process:
            self.log("没有找到需要处理的文件", "WARNING")
            self.processing_complete()
            return
        
        self.log(f"找到 {len(files_to_process)} 个文件需要处理", "INFO")
        
        # 复制目录结构
        try:
            copy_directory_structure(self.input_dir, self.output_dir)
            self.log("目录结构已复制", "SUCCESS")
        except Exception as e:
            self.log(f"复制目录结构失败: {str(e)}", "WARNING")
        
        self.processing = True
        self.stop_btn.config(state='normal')
        
        # 在新线程中处理文件
        self.process_thread = threading.Thread(
            target=self.process_files, 
            args=(files_to_process,)
        )
        self.process_thread.daemon = True
        self.process_thread.start()

    def process_files(self, files_to_process):
        """处理文件的线程函数"""
        total_files = len(files_to_process)
        processed_count = 0
        success_count = 0
        error_count = 0
        
        operation = "加密" if self.encrypt else "解密"
        
        for i, (file_path, rel_path) in enumerate(files_to_process):
            if not self.processing:
                self.log("处理被用户中断", "WARNING")
                break
            
            try:
                # 更新进度显示
                progress = (i / total_files) * 100
                self.progress_bar['value'] = progress
                self.progress_text_var.set(f"{i+1}/{total_files}")
                self.current_file_var.set(rel_path)
                
                self.log(f"正在处理: {rel_path}")
                
                # 计算输出路径
                output_file_path = os.path.join(self.output_dir, rel_path)
                output_dir = os.path.dirname(output_file_path)
                os.makedirs(output_dir, exist_ok=True)
                
                # 处理文件
                success, changes, new_filename, invalid_codes = self.processor.process_file(
                    file_path, output_file_path, self.encrypt
                )
                
                processed_count += 1
                
                # 收集无效代码
                if invalid_codes:
                    self.all_invalid_codes.extend(invalid_codes)
                
                if success:
                    success_count += 1
                    if changes:
                        self.log(f"完成: {rel_path} → {new_filename} (共{len(changes)}处更改)", "SUCCESS")
                        # 记录所有更改详情到日志文件
                        for change in changes:
                            self.log(f"    - {change}", "INFO")
                        
                        # 显示无效代码警告
                        if invalid_codes:
                            for invalid_code in invalid_codes:
                                self.log(f"    ⚠ {invalid_code}", "WARNING")
                    else:
                        self.log(f"完成: {rel_path} → {new_filename} (无需更改)", "SUCCESS")
                else:
                    error_count += 1
                    self.log(f"失败: {rel_path}", "ERROR")
                    for error in changes:
                        self.log(f"    - {error}", "ERROR")
                
            except Exception as e:
                error_count += 1
                self.log(f"处理 {rel_path} 时发生异常: {str(e)}", "ERROR")
        
        # 处理完成
        self.progress_bar['value'] = 100
        
        if self.processing:
            self.log("", "INFO")  # 空行
            self.log(f"=== {operation}处理完成 ===", "SUCCESS")
            self.log(f"总文件数: {total_files}", "INFO")
            self.log(f"处理成功: {success_count}", "SUCCESS")
            if error_count > 0:
                self.log(f"处理失败: {error_count}", "ERROR")
            if self.skipped_doc_files:
                self.log(f"跳过.doc文件: {len(self.skipped_doc_files)}", "SKIP")
            if self.all_invalid_codes:
                unique_codes = list(set(self.all_invalid_codes))
                self.log(f"发现异常代码: {len(unique_codes)}个", "WARNING")
            self.log(f"所有文件已保存到: {self.output_dir}", "INFO")
            
            self.current_file_var.set("处理完成")
            self.progress_text_var.set(f"{processed_count}/{total_files}")
        
        self.processing_complete()

    def processing_complete(self):
        """处理完成后的清理工作"""
        self.processing = False
        self.stop_btn.config(state='disabled')
        self.close_btn.config(state='normal')
        self.export_btn.config(state='normal')
        
        # 自动保存完整日志
        try:
            self.write_log_to_file()
            self.log(f"完整日志已自动保存: {self.log_filename}", "SUCCESS", write_to_file=False)
        except Exception as e:
            self.log(f"自动保存日志失败: {str(e)}", "ERROR", write_to_file=False)
        
        # 显示最终提醒
        self.show_completion_reminders()

    def show_completion_reminders(self):
        """显示处理完成后的提醒"""
        reminders = []
        
        # .doc文件提醒
        if self.skipped_doc_files:
            doc_reminder = f"发现 {len(self.skipped_doc_files)} 个.doc文件被跳过，需要手动处理：\n"
            doc_reminder += "1. 使用Word打开.doc文件\n"
            doc_reminder += "2. 另存为.docx格式\n"
            doc_reminder += "3. 重新放入待处理文件夹进行处理\n\n"
            doc_reminder += "跳过的文件列表:\n"
            for doc_file in self.skipped_doc_files[:10]:
                doc_reminder += f"• {doc_file}\n"
            if len(self.skipped_doc_files) > 10:
                doc_reminder += f"• ... 还有{len(self.skipped_doc_files)-10}个文件\n"
            reminders.append(("需要手动处理.doc文件", doc_reminder))
        
        # 无效代码提醒
        if self.all_invalid_codes:
            unique_codes = list(set(self.all_invalid_codes))
            code_reminder = f"发现 {len(unique_codes)} 个异常的人名代码：\n\n"
            for code in unique_codes[:10]:  # 最多显示10个
                code_reminder += f"• {code}\n"
            if len(unique_codes) > 10:
                code_reminder += f"• ... 还有{len(unique_codes)-10}个异常代码\n"
            code_reminder += f"\n请检查这些代码是否正确。\n完整列表请查看日志文件: {self.log_filename}"
            reminders.append(("发现异常的人名代码", code_reminder))
        
        # 显示提醒对话框
        if reminders:
            self.after(1000, lambda: self.show_reminder_dialogs(reminders))

    def show_reminder_dialogs(self, reminders):
        """显示提醒对话框"""
        for title, message in reminders:
            messagebox.showwarning(title, message)

    def stop_processing(self):
        """停止处理"""
        if self.processing:
            self.processing = False
            self.log("正在停止处理...", "WARNING")

    def on_close(self):
        """关闭窗口"""
        if self.processing:
            if messagebox.askyesno("确认", "处理正在进行中，确定要关闭吗？"):
                self.processing = False
            else:
                return
        
        # 确保日志已保存
        try:
            if self.log_entries:
                self.write_log_to_file()
        except:
            pass
        
        self.destroy()
        self.master.deiconify()

# ========== 单项查询窗口 ==========
class SingleQueryWindow(tk.Toplevel):
    def __init__(self, master, db):
        super().__init__(master)
        self.db = db
        self.title("单项查询")
        self.geometry("900x600")
        self.resizable(False, False)
        self.protocol("WM_DELETE_WINDOW", self.back)
        self.init_ui()

    def init_ui(self):
        # 创建标签页
        notebook = ttk.Notebook(self)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 加密标签页
        encrypt_frame = tk.Frame(notebook)
        notebook.add(encrypt_frame, text="加密查询")
        self.init_encrypt_tab(encrypt_frame)
        
        # 解密标签页
        decrypt_frame = tk.Frame(notebook)
        notebook.add(decrypt_frame, text="解密查询")
        self.init_decrypt_tab(decrypt_frame)

    def init_encrypt_tab(self, parent):
        """初始化加密标签页"""
        # 输入区域
        input_frame = tk.LabelFrame(parent, text="输入信息", font=("微软雅黑", 11, "bold"))
        input_frame.pack(fill='x', padx=10, pady=10)
        
        input_content = tk.Frame(input_frame)
        input_content.pack(padx=15, pady=15)
        
        # 日期输入
        tk.Label(input_content, text="日期:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky='e', padx=5, pady=8)
        tk.Label(input_content, text="(格式: 2024-06-18)", font=("微软雅黑", 8), fg="gray").grid(row=0, column=2, sticky='w', padx=5)
        self.encrypt_date_var = tk.StringVar()
        tk.Entry(input_content, textvariable=self.encrypt_date_var, width=20).grid(row=0, column=1, padx=5)
        
        # 姓名输入
        positions = ["机长", "副驾驶", "观察员", "其他"]
        self.encrypt_name_vars = {}
        
        for i, pos in enumerate(positions, 1):
            tk.Label(input_content, text=f"{pos}姓名:", font=("微软雅黑", 10)).grid(row=i, column=0, sticky='e', padx=5, pady=8)
            var = tk.StringVar()
            self.encrypt_name_vars[pos] = var
            tk.Entry(input_content, textvariable=var, width=20).grid(row=i, column=1, padx=5)
        
        # 按钮
        btn_frame = tk.Frame(input_content)
        btn_frame.grid(row=len(positions)+1, column=0, columnspan=3, pady=15)
        
        tk.Button(btn_frame, text="加密", width=10, font=("微软雅黑", 10), 
                 command=self.do_encrypt).pack(side='left', padx=5)
        tk.Button(btn_frame, text="清空", width=10, font=("微软雅黑", 10), 
                 command=self.clear_encrypt).pack(side='left', padx=5)
        
        # 结果显示区域
        result_frame = tk.LabelFrame(parent, text="加密结果", font=("微软雅黑", 11, "bold"))
        result_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        result_content = tk.Frame(result_frame)
        result_content.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.encrypt_result_text = tk.Text(result_content, font=("微软雅黑", 10))
        encrypt_scrollbar = tk.Scrollbar(result_content, orient="vertical", command=self.encrypt_result_text.yview)
        self.encrypt_result_text.configure(yscrollcommand=encrypt_scrollbar.set)
        
        self.encrypt_result_text.pack(side="left", fill="both", expand=True)
        encrypt_scrollbar.pack(side="right", fill="y")
        
        # 复制按钮
        copy_btn_frame = tk.Frame(result_frame)
        copy_btn_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Button(copy_btn_frame, text="复制结果", width=12, font=("微软雅黑", 10), 
                 command=self.copy_encrypt_result).pack(side='right')

    def init_decrypt_tab(self, parent):
        """初始化解密标签页"""
        # 输入区域
        input_frame = tk.LabelFrame(parent, text="输入编码", font=("微软雅黑", 11, "bold"))
        input_frame.pack(fill='x', padx=10, pady=10)
        
        input_content = tk.Frame(input_frame)
        input_content.pack(padx=15, pady=15)
        
        # 日期输入
        tk.Label(input_content, text="加密日期:", font=("微软雅黑", 10)).grid(row=0, column=0, sticky='e', padx=5, pady=8)
        tk.Label(input_content, text="(格式: 2024年6月AB日)", font=("微软雅黑", 8), fg="gray").grid(row=0, column=2, sticky='w', padx=5)
        self.decrypt_date_var = tk.StringVar()
        tk.Entry(input_content, textvariable=self.decrypt_date_var, width=25).grid(row=0, column=1, padx=5)
        
        # 编码输入
        positions = ["机长", "副驾驶", "观察员", "其他"]
        self.decrypt_code_vars = {}
        
        for i, pos in enumerate(positions, 1):
            tk.Label(input_content, text=f"{pos}编码:", font=("微软雅黑", 10)).grid(row=i, column=0, sticky='e', padx=5, pady=8)
            var = tk.StringVar()
            self.decrypt_code_vars[pos] = var
            tk.Entry(input_content, textvariable=var, width=25).grid(row=i, column=1, padx=5)
        
        # 按钮
        btn_frame = tk.Frame(input_content)
        btn_frame.grid(row=len(positions)+1, column=0, columnspan=3, pady=15)
        
        tk.Button(btn_frame, text="解密", width=10, font=("微软雅黑", 10), 
                 command=self.do_decrypt).pack(side='left', padx=5)
        tk.Button(btn_frame, text="清空", width=10, font=("微软雅黑", 10), 
                 command=self.clear_decrypt).pack(side='left', padx=5)
        
        # 结果显示区域
        result_frame = tk.LabelFrame(parent, text="解密结果", font=("微软雅黑", 11, "bold"))
        result_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        result_content = tk.Frame(result_frame)
        result_content.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.decrypt_result_text = tk.Text(result_content, font=("微软雅黑", 10))
        decrypt_scrollbar = tk.Scrollbar(result_content, orient="vertical", command=self.decrypt_result_text.yview)
        self.decrypt_result_text.configure(yscrollcommand=decrypt_scrollbar.set)
        
        self.decrypt_result_text.pack(side="left", fill="both", expand=True)
        decrypt_scrollbar.pack(side="right", fill="y")
        
        # 复制按钮
        copy_btn_frame = tk.Frame(result_frame)
        copy_btn_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Button(copy_btn_frame, text="复制结果", width=12, font=("微软雅黑", 10), 
                 command=self.copy_decrypt_result).pack(side='right')
        
        # 返回按钮
        back_btn_frame = tk.Frame(parent)
        back_btn_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Button(back_btn_frame, text="返回主界面", width=15, font=("微软雅黑", 10), 
                 command=self.back).pack(side='right')

    def do_encrypt(self):
        """执行加密"""
        try:
            self.encrypt_result_text.delete(1.0, tk.END)
            results = []
            invalid_codes = []
            
            # 处理日期
            date_str = self.encrypt_date_var.get().strip()
            if date_str:
                try:
                    encrypted_date = encode_date_full(date_str)
                    results.append(f"日期: {date_str} → {encrypted_date}")
                except Exception as e:
                    results.append(f"日期加密失败: {date_str} ({str(e)})")
            
            # 处理姓名
            for position, var in self.encrypt_name_vars.items():
                name = var.get().strip()
                if name:
                    try:
                        emp_id = name_to_empid(self.db, name)
                        encrypted_name = encode_employee_id(emp_id)
                        
                        # 验证加密后的代码格式
                        if not validate_employee_code(encrypted_name):
                            invalid_codes.append(f"{position}: {name} → {encrypted_name} (格式异常)")
                        
                        results.append(f"{position}: {name} → {encrypted_name}")
                    except Exception as e:
                        results.append(f"{position}加密失败: {name} ({str(e)})")
            
            if results:
                result_text = "\n".join(results)
                if invalid_codes:
                    result_text += "\n\n⚠️ 代码格式异常:\n" + "\n".join(invalid_codes)
                self.encrypt_result_text.insert(1.0, result_text)
                
                # 如果有无效代码，弹窗提醒
                if invalid_codes:
                    messagebox.showwarning("代码异常", 
                                         f"发现 {len(invalid_codes)} 个非4位的人名代码，请检查！")
            else:
                self.encrypt_result_text.insert(1.0, "请输入需要加密的信息")
                
        except Exception as e:
            messagebox.showerror("加密失败", str(e))

    def do_decrypt(self):
        """执行解密"""
        try:
            self.decrypt_result_text.delete(1.0, tk.END)
            results = []
            invalid_codes = []
            
            # 处理日期
            date_str = self.decrypt_date_var.get().strip()
            if date_str:
                try:
                    decrypted_date = decode_date_full(date_str)
                    results.append(f"日期: {date_str} → {decrypted_date}")
                except Exception as e:
                    results.append(f"日期解密失败: {date_str} ({str(e)})")
            
            # 处理编码
            for position, var in self.decrypt_code_vars.items():
                code = var.get().strip()
                if code:
                    try:
                        # 修正：验证代码格式
                        if not code.startswith("N_"):
                            invalid_codes.append(f"{position}: {code} (前缀异常)")
                            continue
                        
                        code_part = code[2:]  # 去掉 "N_" 前缀
                        if len(code_part) != 4:
                            invalid_codes.append(f"{position}: {code} (编码部分{len(code_part)}位)")
                            continue
                        
                        emp_id = decode_employee_id(code)
                        info = empid_to_info(self.db, emp_id)
                        detail = f"{position}: {code} → {info['姓名']} (工号:{info['员工号']}, 型别:{info['型别']}, 技术资格:{info['技术资格']})"
                        results.append(detail)
                    except Exception as e:
                        results.append(f"{position}解密失败: {code} ({str(e)})")
                        invalid_codes.append(f"{position}: {code} (解密失败)")
            
            if results:
                result_text = "\n".join(results)
                if invalid_codes:
                    result_text += "\n\n⚠️ 代码异常:\n" + "\n".join(invalid_codes)
                self.decrypt_result_text.insert(1.0, result_text)
                
                # 如果有无效代码，弹窗提醒
                if invalid_codes:
                    messagebox.showwarning("代码异常", 
                                         f"发现 {len(invalid_codes)} 个异常的人名代码，请检查！")
            else:
                self.decrypt_result_text.insert(1.0, "请输入需要解密的编码")
                
        except Exception as e:
            messagebox.showerror("解密失败", str(e))

    def clear_encrypt(self):
        """清空加密输入"""
        self.encrypt_date_var.set('')
        for var in self.encrypt_name_vars.values():
            var.set('')
        self.encrypt_result_text.delete(1.0, tk.END)

    def clear_decrypt(self):
        """清空解密输入"""
        self.decrypt_date_var.set('')
        for var in self.decrypt_code_vars.values():
            var.set('')
        self.decrypt_result_text.delete(1.0, tk.END)

    def copy_encrypt_result(self):
        """复制加密结果"""
        try:
            result = self.encrypt_result_text.get(1.0, tk.END).strip()
            if result:
                self.clipboard_clear()
                self.clipboard_append(result)
                messagebox.showinfo("成功", "加密结果已复制到剪切板")
            else:
                messagebox.showwarning("提示", "没有可复制的内容")
        except Exception as e:
            messagebox.showerror("复制失败", str(e))

    def copy_decrypt_result(self):
        """复制解密结果"""
        try:
            result = self.decrypt_result_text.get(1.0, tk.END).strip()
            if result:
                self.clipboard_clear()
                self.clipboard_append(result)
                messagebox.showinfo("成功", "解密结果已复制到剪切板")
            else:
                messagebox.showwarning("提示", "没有可复制的内容")
        except Exception as e:
            messagebox.showerror("复制失败", str(e))

    def back(self):
        """返回主界面"""
        self.destroy()
        self.master.deiconify()

# ========== 启动程序 ==========
if __name__ == '__main__':
    try:
        app = MainApp()
        app.mainloop()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")






